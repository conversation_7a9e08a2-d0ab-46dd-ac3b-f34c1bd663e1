<% content_for :title, "Referral Code: #{@code.code}" %>

<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Referral Code: <code><%= @code.code %></code></h1>
    <%= link_to "Back to Codes", admin_referral_codes_path, class: "btn btn-secondary" %>
  </div>

  <div class="row">
    <div class="col-md-8">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">Code Details</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <p><strong>Code:</strong> <code><%= @code.code %></code></p>
              <p><strong>Status:</strong> 
                <span class="badge badge-<%= @code.status == 'active' ? 'success' : 'secondary' %>">
                  <%= @code.status.humanize %>
                </span>
              </p>
              <p><strong>Upgrade To:</strong> 
                <span class="badge badge-primary">
                  <%= @code.tier_upgrade_to.humanize %>
                </span>
              </p>
              <p><strong>Duration:</strong> <%= pluralize(@code.duration_months, 'month') %></p>
            </div>
            <div class="col-md-6">
              <p><strong>Usage:</strong> 
                <span class="<%= @code.current_uses >= @code.max_uses ? 'text-danger' : 'text-success' %>">
                  <%= @code.current_uses %> / <%= @code.max_uses %>
                </span>
              </p>
              <p><strong>Created:</strong> <%= @code.created_at.strftime("%b %d, %Y") %></p>
              <p><strong>Created By:</strong> <%= @code.created_by.email %></p>
              <p><strong>Expires:</strong> 
                <% if @code.expires_at %>
                  <%= @code.expires_at.strftime("%b %d, %Y") %>
                  <% if @code.expires_at.past? %>
                    <span class="text-danger">(Expired)</span>
                  <% end %>
                <% else %>
                  <span class="text-muted">Never</span>
                <% end %>
              </p>
            </div>
          </div>
          <% if @code.description.present? %>
            <hr>
            <p><strong>Description:</strong></p>
            <p><%= simple_format(@code.description) %></p>
          <% end %>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Update Code</h5>
        </div>
        <div class="card-body">
          <%= form_with model: [:admin, @code], local: true do |form| %>
            <% if @code.errors.any? %>
              <div class="alert alert-danger">
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0">
                  <% @code.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <%= form.label :status %>
                  <%= form.select :status, 
                      options_for_select([
                        ['Active', 'active'],
                        ['Expired', 'expired'],
                        ['Used Up', 'used_up'],
                        ['Disabled', 'disabled']
                      ], @code.status), 
                      {}, 
                      { class: "form-control" } %>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <%= form.label :expires_at, "Expires At" %>
                  <%= form.datetime_local_field :expires_at, 
                      value: @code.expires_at&.strftime("%Y-%m-%dT%H:%M"),
                      class: "form-control" %>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <%= form.label :description %>
              <%= form.text_area :description, class: "form-control", rows: 3 %>
            </div>
            
            <div class="form-group">
              <%= form.submit "Update Code", class: "btn btn-primary" %>
              <%= link_to "Delete Code", admin_referral_code_path(@code), 
                  method: :delete, 
                  class: "btn btn-outline-danger ml-2",
                  confirm: "Are you sure you want to delete this code?" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Usage Stats</h5>
        </div>
        <div class="card-body text-center">
          <h3 class="text-primary"><%= @code.current_uses %></h3>
          <p class="text-muted">Times Used</p>
          
          <h3 class="text-info"><%= @code.max_uses - @code.current_uses %></h3>
          <p class="text-muted">Uses Remaining</p>
          
          <% if @code.expires_at %>
            <% days_until_expiry = (@code.expires_at.to_date - Date.current).to_i %>
            <h3 class="<%= days_until_expiry <= 7 ? 'text-warning' : 'text-success' %>">
              <%= days_until_expiry %>
            </h3>
            <p class="text-muted">Days Until Expiry</p>
          <% end %>
          
          <% usage_percentage = (@code.current_uses.to_f / @code.max_uses * 100).round(1) %>
          <div class="progress mt-3">
            <div class="progress-bar <%= usage_percentage >= 100 ? 'bg-danger' : 'bg-primary' %>" 
                 style="width: <%= [usage_percentage, 100].min %>%">
              <%= usage_percentage %>%
            </div>
          </div>
          <small class="text-muted mt-2 d-block">
            <%= @code.current_uses %> of <%= @code.max_uses %> uses
          </small>
        </div>
      </div>
    </div>
  </div>
</div>